"""
混合认证中间件：MAC地址预认证 + Token认证
支持MAC地址白名单预认证，如果MAC地址认证失败则回退到Token认证
"""

import asyncio
from typing import Dict, Any
from loguru import logger

from core.auth import AuthMiddleware


class HybridAuthMiddleware:
    """混合认证中间件：MAC地址预认证 + Token认证"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mac_auth_enabled = config.get("mac_auth", {}).get("enabled", False)

        # 初始化Token认证中间件
        self.token_auth = AuthMiddleware(config)

        # API配置
        self.api_config = config.get("manager-api", {})

        logger.info(f"混合认证中间件初始化完成: MAC认证{'启用' if self.mac_auth_enabled else '禁用'}")

    async def authenticate(self, headers: Dict[str, str]) -> bool:
        """
        混合认证流程：
        1. 如果启用MAC认证，首先检查MAC地址白名单
        2. MAC认证通过则直接放行
        3. MAC认证失败则使用Token认证
        """
        device_mac = headers.get("device-id", "")

        # 1. 尝试MAC地址预认证
        if self.mac_auth_enabled and device_mac:
            try:
                if await self._check_mac_whitelist(device_mac):
                    logger.info(f"MAC地址预认证通过: {device_mac}")
                    headers["auth_success"] = "true"
                    headers["auth_method"] = "mac_whitelist"
                    headers["device_bound"] = "true"
                    return True
                else:
                    logger.info(f"MAC地址不在白名单中: {device_mac}，继续Token认证")
            except Exception as e:
                logger.warning(f"MAC地址认证失败: {str(e)}，回退到Token认证")

        # 2. 回退到Token认证
        logger.info("使用Token认证")
        return await self.token_auth.authenticate(headers)

    async def _check_mac_whitelist(self, mac_address: str) -> bool:
        """实时检查MAC地址是否在白名单中"""
        if not self.api_config.get("url") or not self.api_config.get("secret"):
            logger.warning("API配置不完整，无法检查MAC地址白名单")
            return False

        import aiohttp

        url = f"{self.api_config['url']}/device/auth/check/{mac_address}"
        headers = {
            "Authorization": f"Bearer {self.api_config['secret']}",
            "Content-Type": "application/json"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            is_whitelisted = data.get("data", False)
                            logger.info(f"MAC地址 {mac_address} 白名单检查结果: {is_whitelisted}")
                            return is_whitelisted
                        else:
                            logger.warning(f"MAC地址检查API返回错误: {data}")
                            return False
                    else:
                        logger.error(f"MAC地址检查API请求失败: HTTP {response.status}")
                        return False
        except asyncio.TimeoutError:
            logger.error(f"MAC地址检查超时: {mac_address}")
            return False
        except Exception as e:
            logger.error(f"MAC地址检查失败: {str(e)}")
            return False
