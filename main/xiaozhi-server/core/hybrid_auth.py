"""
混合认证中间件：MAC地址预认证 + Token认证
支持MAC地址白名单预认证，如果MAC地址认证失败则回退到Token认证
"""

import asyncio
import json
import time
from typing import Dict, Any
from loguru import logger

from core.auth import AuthMiddleware
from core.exceptions import AuthenticationError


class HybridAuthMiddleware:
    """混合认证中间件：MAC地址预认证 + Token认证"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mac_auth_enabled = config.get("mac_auth", {}).get("enabled", False)
        
        # 初始化Token认证中间件
        self.token_auth = AuthMiddleware(config)
        
        # MAC地址白名单缓存
        self.mac_whitelist = set()
        self.mac_cache_ttl = config.get("mac_auth", {}).get("cache_ttl", 300)  # 5分钟
        self.last_cache_update = 0
        
        # API配置
        self.api_config = config.get("manager-api", {})
        
        logger.info(f"混合认证中间件初始化完成: MAC认证{'启用' if self.mac_auth_enabled else '禁用'}")
    
    async def authenticate(self, headers: Dict[str, str]) -> bool:
        """
        混合认证流程：
        1. 如果启用MAC认证，首先检查MAC地址白名单
        2. MAC认证通过则直接放行
        3. MAC认证失败则使用Token认证
        """
        device_mac = headers.get("device-id", "")
        
        # 1. 尝试MAC地址预认证
        if self.mac_auth_enabled and device_mac:
            try:
                if await self._check_mac_whitelist(device_mac):
                    logger.info(f"MAC地址预认证通过: {device_mac}")
                    headers["auth_success"] = "true"
                    headers["auth_method"] = "mac_whitelist"
                    headers["device_bound"] = "true"
                    return True
                else:
                    logger.info(f"MAC地址不在白名单中: {device_mac}，继续Token认证")
            except Exception as e:
                logger.warning(f"MAC地址认证失败: {str(e)}，回退到Token认证")
        
        # 2. 回退到Token认证
        logger.info("使用Token认证")
        return await self.token_auth.authenticate(headers)
    
    async def _check_mac_whitelist(self, mac_address: str) -> bool:
        """检查MAC地址是否在白名单中"""
        # 更新缓存（如果需要）
        await self._update_mac_cache_if_needed()
        
        # 检查白名单
        return mac_address.lower() in self.mac_whitelist
    
    async def _update_mac_cache_if_needed(self):
        """如果缓存过期则更新MAC地址白名单缓存"""
        current_time = time.time()
        if current_time - self.last_cache_update > self.mac_cache_ttl:
            try:
                await self._update_mac_cache()
                self.last_cache_update = current_time
                logger.info(f"MAC地址白名单缓存已更新，共 {len(self.mac_whitelist)} 个地址")
            except Exception as e:
                logger.error(f"更新MAC地址白名单缓存失败: {str(e)}")
    
    async def _update_mac_cache(self):
        """从API获取MAC地址白名单并更新缓存"""
        if not self.api_config.get("url") or not self.api_config.get("secret"):
            logger.warning("API配置不完整，无法获取MAC地址白名单")
            return
        
        import aiohttp
        
        url = f"{self.api_config['url']}/device/maclist"
        headers = {
            "Authorization": f"Bearer {self.api_config['secret']}",
            "Content-Type": "application/json"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0 and data.get("data"):
                            # 提取MAC地址并转换为小写
                            mac_list = data["data"]
                            self.mac_whitelist = {
                                item["macAddress"].lower() 
                                for item in mac_list 
                                if item.get("macAddress")
                            }
                            logger.info(f"成功获取MAC地址白名单: {len(self.mac_whitelist)} 个地址")
                        else:
                            logger.warning(f"API返回错误: {data}")
                    else:
                        logger.error(f"API请求失败: HTTP {response.status}")
        except asyncio.TimeoutError:
            logger.error("获取MAC地址白名单超时")
        except Exception as e:
            logger.error(f"获取MAC地址白名单失败: {str(e)}")
